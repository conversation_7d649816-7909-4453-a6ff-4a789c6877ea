"use server";
import { prisma } from "@/src/lib/prisma";
import { parseCurrencyToNumber, parseObject } from "@/src/lib/utils";
import { Periodicity } from "@/src/types/common";
import {
  Proposal,
  ProposalFilters,
  ProposalSituation,
} from "@/src/types/core/proposal";

import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { ProposalSchema } from "@/src/app/views/crm/proposals/_schemas/proposals.schema";
import { removeFile } from "./files";
import { removeInspectionParameters } from "./inspection-parameters";
import { removeRepairBudgets } from "./repair-budget";

// Função auxiliar para processar propostas antes de enviá-las ao frontend
function processProposals(proposals: any[]) {
  return proposals.map((proposal) => {
    // Processar datas
    const processedProposal = {
      ...proposal,
      // Garantir que as datas sejam válidas
      startDate:
        proposal.startDate instanceof Date &&
        !isNaN(proposal.startDate.getTime())
          ? proposal.startDate
          : null,
      endDate:
        proposal.endDate instanceof Date && !isNaN(proposal.endDate.getTime())
          ? proposal.endDate
          : null,
      createdAt:
        proposal.createdAt instanceof Date &&
        !isNaN(proposal.createdAt.getTime())
          ? proposal.createdAt
          : new Date(),
      updatedAt:
        proposal.updatedAt instanceof Date &&
        !isNaN(proposal.updatedAt.getTime())
          ? proposal.updatedAt
          : new Date(),

      // Garantir que os valores numéricos sejam válidos
      budget:
        typeof proposal.budget === "object" && proposal.budget !== null
          ? Number(proposal.budget.toString())
          : typeof proposal.budget === "number"
          ? proposal.budget
          : 0,
      area:
        typeof proposal.area === "object" && proposal.area !== null
          ? Number(proposal.area.toString())
          : typeof proposal.area === "number"
          ? proposal.area
          : 0,
      downPayment: proposal.downPayment
        ? typeof proposal.downPayment === "object"
          ? Number(proposal.downPayment.toString())
          : typeof proposal.downPayment === "number"
          ? proposal.downPayment
          : 0
        : 0,
      installmentAmount: proposal.installmentAmount
        ? typeof proposal.installmentAmount === "object"
          ? Number(proposal.installmentAmount.toString())
          : typeof proposal.installmentAmount === "number"
          ? proposal.installmentAmount
          : 0
        : 0,
      installmentNumber: proposal.installmentNumber
        ? Number(proposal.installmentNumber)
        : 0,
    };

    return processedProposal;
  });
}

export async function loadProposals(filters?: ProposalFilters) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Extrair parâmetros de paginação e busca
    const page = filters?.page ? Number(filters.page) : 1;
    const pageSize = filters?.pageSize ? Number(filters.pageSize) : 10;
    const search = filters?.search || "";
    const skip = (page - 1) * pageSize;

    const query: any = {
      include: {
        customer: { include: { contacts: { orderBy: { date: "desc" } } } },
        plannings: true,
        serviceScopes: true,
        proposalTemplate: true,
        file: true,
        contract: true,
      },
      where: {
        customer: {
          organizationId,
        },
        // Adicionar busca por nome de proposta e nome de cliente
        ...(search
          ? {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                {
                  customer: { name: { contains: search, mode: "insensitive" } },
                },
                // Buscar também no campo customService se existir
                { customService: { contains: search, mode: "insensitive" } },
              ],
            }
          : {}),
      },
      orderBy: [{ createdAt: "desc" }],
    };

    // Adicionar paginação se solicitado
    if (filters?.page) {
      query.skip = skip;
      query.take = pageSize;
    }

    if (filters?.situation) {
      query.where = { ...query.where, situation: { in: filters.situation } };
    }

    if (filters?.customerId) {
      query.where = { ...query.where, customerId: filters.customerId };
    }

    if (filters?.startDate) {
      query.where = { ...query.where, startDate: new Date(filters.startDate) };
    }

    // Adicionar filtro por tipo de serviço
    if (filters?.serviceType) {
      const serviceTypeFilter = Array.isArray(filters.serviceType)
        ? filters.serviceType
        : [filters.serviceType];

      query.where = {
        ...query.where,
        serviceType: { in: serviceTypeFilter },
      };
    }

    // Contar o total de registros para paginação
    const countQuery = { ...query };
    delete countQuery.skip;
    delete countQuery.take;
    delete countQuery.include;
    delete countQuery.orderBy;

    const [total, data] = await Promise.all([
      prisma.proposal.count(countQuery),
      prisma.proposal.findMany(query),
    ]);

    // Processar as propostas antes de enviá-las ao frontend
    const processedData = processProposals(data);

    // Se a paginação foi solicitada, retornar informações de paginação
    if (filters?.page) {
      return {
        data: parseObject(processedData) as Proposal[],
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      };
    }

    // Caso contrário, retornar apenas os dados
    return parseObject(processedData) as Proposal[];
  } catch (error) {
    console.error(error);
  }
}

export async function findProposal(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.proposal.findUnique({
      where: {
        id,
        customer: {
          organizationId,
        },
      },
      include: {
        customer: true,
        serviceScopes: true,
        plannings: {
          orderBy: {
            order: "asc",
          },
        },
      },
    });

    // Processar a proposta antes de enviá-la ao frontend
    if (data) {
      const processedData = processProposals([data])[0];
      return parseObject(processedData) as Proposal;
    }

    return null;
  } catch (error) {
    console.error(error);
  }
}

// Alias para findProposal para compatibilidade com o código existente
export const getProposalById = findProposal;

async function logProposalStatusChange(
  proposalId: string,
  oldStatus: ProposalSituation,
  newStatus: ProposalSituation
) {
  await prisma.logProposal.create({
    data: {
      proposalId,
      oldStatus,
      newStatus,
    },
  });
}

export async function saveProposal(proposalData: ProposalSchema) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const customer = await prisma.customer.findUnique({
      where: {
        id: proposalData.customerId,
        organizationId,
      },
    });

    const proposalTemplate = await prisma.proposalTemplate.findUnique({
      where: { id: proposalData.proposalTemplateId },
      include: { fileEditor: true },
    });

    const serviceScopes = await prisma.serviceScope.findMany({
      where: { id: { in: proposalData.serviceScopes } },
    });

    if (customer && serviceScopes && proposalTemplate) {
      const where = { id: proposalData.id };
      const include = {
        plannings: true,
        serviceScopes: true,
        customer: true,
        file: true,
      };

      const {
        name,
        startDate,
        endDate,
        budget,
        paymentCondition,
        situation,
        periodicity,
        plannings,
        installmentAmount,
        installmentNumber,
        area,
        cep,
        city,
        state,
        address,
        methodology,
        downPayment,
        serviceType,
        customService,
      } = proposalData;

      // Preparar dados base
      const data = {
        name,
        startDate,
        endDate,
        budget: parseCurrencyToNumber(budget.toString()),
        workTotalCost: parseCurrencyToNumber(
          proposalData.workTotalCost?.toString() || budget.toString()
        ), // Usar workTotalCost se existir, senão usar budget
        cep,
        city,
        state,
        address,
        methodology,
        customService,
        area: Number(area),
        paymentCondition,
        downPayment: parseCurrencyToNumber(downPayment?.toString() || "0"),
        situation: situation as ProposalSituation,
        periodicity: periodicity as Periodicity,
        installmentAmount: parseCurrencyToNumber(
          installmentAmount?.toString() || "0"
        ),
        installmentNumber: installmentNumber
          ? Number(installmentNumber)
          : undefined,
        customer: { connect: { id: customer.id } },
        proposalTemplate: { connect: { id: proposalTemplate.id } },
        plannings: { createMany: { data: plannings! } },
        serviceScopes: {
          connect: serviceScopes.map(({ id }) => ({
            id,
          })),
        },
      } as any;

      // Adicionar serviceType de forma segura
      if (typeof serviceType === "string" && serviceType.trim() !== "") {
        data.serviceType = serviceType.trim();
        console.log(`Definindo serviceType: ${serviceType.trim()}`);
      } else {
        console.log("serviceType não definido ou inválido:", serviceType);
      }

      let proposal;

      // let proposal = await (proposalData.id
      //   ? prisma.proposal.update({ where, data, include })
      //   : prisma.proposal.create({ data, include }));

      let currentSituation: ProposalSituation | undefined;

      if (proposalData.id) {
        // Buscar a situação atual da proposta antes de atualizar
        const existingProposal = await prisma.proposal.findUnique({
          where: { id: proposalData.id },
          select: { situation: true },
        });

        if (existingProposal) {
          currentSituation = existingProposal.situation as ProposalSituation;
        }

        // Atualização de proposta existente
        // Primeiro, deletar plannings existentes
        await prisma.planningFrequencyItem.deleteMany({
          where: { proposalId: proposalData.id },
        });

        // Depois, atualizar a proposta com novos plannings
        proposal = await prisma.proposal.update({
          where,
          data: {
            ...data,
            plannings: {
              create: plannings!.map((planning) => ({
                order: planning.order,
                content: planning.content,
                label: planning.label,
              })),
            },
          },
          include,
        });
      } else {
        // Criação de nova proposta
        proposal = await prisma.proposal.create({
          data: {
            ...data,
            plannings: {
              create: plannings!.map((planning) => ({
                order: planning.order,
                content: planning.content,
                label: planning.label,
              })),
            },
          },
          include,
        });
      }

      // Sempre gerar um novo arquivo quando a proposta for editada ou criada
      // Gerar variáveis para o template usando a função auxiliar
      const variables = await generateProposalTemplateVariables(proposal);
      // Substituir as variáveis no template
      const fileName = `${Date.now()}-proposta-${proposalTemplate.title}`;
      const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
        `${proposalTemplate?.fileEditorId}`,
        variables,
        fileName
      );

      if (replacedFileEditor) {
        proposal = await prisma.proposal.update({
          where: { id: proposal.id },
          data: { fileEditorId: replacedFileEditor.id },
          include,
        });
      }

      // Se houver mudança de situação, registrar no log
      if (currentSituation && currentSituation !== proposalData.situation) {
        await logProposalStatusChange(
          proposal.id,
          currentSituation,
          proposalData.situation as ProposalSituation
        );
      }

      return parseObject(proposal) as Proposal;
    }
  } catch (error) {
    console.error(error);
  }
}

export async function updateProposalsPositions(proposals: Proposal[]) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const updatedProposals = await Promise.all(
      proposals.map(async (proposal) => {
        try {
          const currentProposal = await prisma.proposal.findUnique({
            where: { id: proposal.id },
            select: { situation: true },
          });

          // Verificar se a proposta está sendo movida para "Proposta aceita"
          const isMovingToAccepted =
            currentProposal &&
            currentProposal.situation !== "PROPOSAL_ACCEPTED" &&
            proposal.situation === "PROPOSAL_ACCEPTED";

          // Preparar os dados para atualização
          const updateData = {
            order: proposal.order,
            situation: proposal.situation as ProposalSituation,
          };

          console.log(`Atualizando proposta ${proposal.id}:`, {
            situacaoAtual: currentProposal?.situation,
            novaSituacao: proposal.situation,
            isMovingToAccepted,
          });

          // Atualizar a proposta
          const updatedProposal = await prisma.proposal.update({
            where: {
              id: proposal.id,
              customer: {
                organizationId,
              },
            },
            data: updateData,
          });

          // Se a proposta está sendo movida para "Proposta aceita", registrar no log
          if (isMovingToAccepted) {
            console.log(
              `Proposta ${proposal.id} movida para PROPOSAL_ACCEPTED. O usuário deve selecionar um template de contrato.`
            );
          }

          // Se houver mudança de situação, registrar no log
          if (
            currentProposal &&
            currentProposal.situation !== proposal.situation
          ) {
            await logProposalStatusChange(
              proposal.id,
              currentProposal.situation,
              proposal.situation as ProposalSituation
            );
          }

          // Chama a função para verificar e criar contrato se necessário
          await createContractForProposal(
            updatedProposal.id,
            updatedProposal.situation
          );

          return updatedProposal;
        } catch (error) {
          console.error(error);
          return null;
        }
      })
    );

    return parseObject(updatedProposals) as Proposal[];
  } catch (error) {
    console.error(error);
  }
}

export async function createContractForProposal(
  proposalId: string,
  situation: ProposalSituation
) {
  const eligibleSituations: ProposalSituation[] = [
    "PROPOSAL_ACCEPTED",
    "SIGN_REQUESTED",
    "SIGNED",
    "PROJECT_IN_PROGRESS",
    "PROJECT_FINISHED",
  ];

  if (!eligibleSituations.includes(situation)) return;

  try {
    // Buscar a proposta com todos os dados necessários
    const proposal = await prisma.proposal.findUnique({
      where: { id: proposalId },
      include: {
        customer: true,
        proposalTemplate: true,
        file: true,
        contract: true,
      },
    });

    if (!proposal) {
      console.error(`Proposta não encontrada: ${proposalId}`);
      return;
    }

    // Verificar se já existe um contrato para essa proposta
    if (proposal.contract) {
      // Se já existe um contrato mas não tem arquivo, gerar o arquivo
      if (!proposal.contract.fileEditorId) {
        // Gerar variáveis para o template usando a função auxiliar
        const variables = await generateProposalTemplateVariables(proposal);

        // Buscar o template de contrato
        const contractTemplate = await prisma.proposalTemplate.findFirst({
          where: {
            type: "CONTRACT",
            ...(proposal.proposalTemplateId
              ? { id: proposal.proposalTemplateId }
              : {}),
          },
        });

        if (contractTemplate && contractTemplate.fileEditorId) {
          // Substituir as variáveis no template de contrato
          const fileName = `${Date.now()}-contrato-${proposal.name}`;
          const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
            contractTemplate.fileEditorId,
            variables,
            fileName
          );

          if (replacedFileEditor) {
            // Atualizar o contrato com o arquivo gerado
            await prisma.contract.update({
              where: { id: proposal.contract.id },
              data: { fileEditorId: replacedFileEditor.id },
            });

            console.log(
              `Arquivo de contrato gerado com sucesso para a proposta ${proposalId}`
            );
          }
        }
      }
    } else {
      // Criar um novo contrato
      const newContract = await prisma.contract.create({
        data: {
          proposalId,
        },
      });

      // Gerar o arquivo de contrato
      // Gerar variáveis para o template usando a função auxiliar
      const variables = await generateProposalTemplateVariables(proposal);

      // Buscar o template de contrato
      const contractTemplate = await prisma.proposalTemplate.findFirst({
        where: {
          type: "CONTRACT",
          ...(proposal.proposalTemplateId
            ? { id: proposal.proposalTemplateId }
            : {}),
        },
      });

      if (contractTemplate && contractTemplate.fileEditorId) {
        // Substituir as variáveis no template de contrato
        const fileName = `${Date.now()}-contrato-${proposal.name}`;
        const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
          contractTemplate.fileEditorId,
          variables,
          fileName
        );

        if (replacedFileEditor) {
          // Atualizar o contrato com o arquivo gerado
          await prisma.contract.update({
            where: { id: newContract.id },
            data: { fileEditorId: replacedFileEditor.id },
          });

          console.log(
            `Arquivo de contrato gerado com sucesso para a proposta ${proposalId}`
          );
        }
      }
    }
  } catch (error) {
    console.error(
      `Erro ao criar contrato para a proposta ${proposalId}:`,
      error
    );
  }
}

export async function removeProposal(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const proposal = await prisma.proposal.findUnique({
      where: {
        id,
        customer: {
          organizationId,
        },
      },
      include: { file: true, repairBudgets: true, inspectionParameters: true },
    });

    if (!proposal) {
      throw new Error("Proposta não existe ou não pertence a sua organização");
    }

    // Excluir todos os registros relacionados à proposta
    await prisma.planningFrequencyItem.deleteMany({
      where: { proposalId: id },
    });
    await prisma.productivity.deleteMany({
      where: { proposalId: id },
    });
    await prisma.contract.deleteMany({
      where: { proposalId: id },
    });
    // Excluir logs de proposta para evitar violação de chave estrangeira
    await prisma.logProposal.deleteMany({
      where: { proposalId: id },
    });

    if (proposal.file) {
      await removeFile(proposal.file);
    }

    if (proposal.repairBudgets.length) {
      await removeRepairBudgets(id);
    }

    if (proposal.inspectionParameters.length) {
      await removeInspectionParameters(id);
    }

    await prisma.proposal.delete({ where: { id } });

    return { message: "Proposta removida com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function fetchProposalsGroupedByDate() {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        customer: {
          organizationId,
        },
      },
      _count: { id: true },
      orderBy: { startDate: "asc" },
    });

    // Processar as datas antes de enviá-las ao frontend
    const processedData = data.map((item) => ({
      ...item,
      startDate:
        item.startDate instanceof Date && !isNaN(item.startDate.getTime())
          ? item.startDate
          : null,
    }));

    return parseObject(processedData) as Proposal[];
  } catch (error) {
    console.error(error);
  }
}

// Função para formatar datas no padrão DD/MM/YYYY
function formatDate(date: number | string) {
  return new Intl.DateTimeFormat("pt-BR").format(new Date(date));
}

// Função para formatar valores monetários no padrão R$ 9.999,99
function formatCurrency(value: number) {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

/**
 * Função auxiliar para gerar variáveis para templates de proposta
 * Esta função facilita a adição de novas variáveis no futuro
 */
import { currencyToWords } from "@/src/lib/number-to-words";

export async function generateProposalTemplateVariables(proposal: any) {
  // Calcular o número de dias entre a data de início e fim
  const startDateObj = new Date(proposal.startDate);
  const endDateObj = new Date(proposal.endDate);
  const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Calcular 80% do valor da proposta
  const valor80Porcento = Number(proposal.budget) * 0.8;

  // Converter valores para texto por extenso
  const valorPropostaExtenso = await currencyToWords(
    Number(proposal.budget) || 0
  );
  const valorParcelaExtenso = await currencyToWords(
    Number(proposal.installmentAmount) || 0
  );
  const valorEntradaExtenso = await currencyToWords(
    Number(proposal.downPayment) || 0
  );
  const valor80PorcentoExtenso = await currencyToWords(valor80Porcento);

  // Formatar endereço completo com CEP formatado
  const enderecoCompleto = `${proposal.address || ""}, CEP: ${
    proposal.cep || ""
  }, ${proposal.city || ""}/${proposal.state || ""}`;

  // Mapeamento das variáveis
  return {
    // Informações da proposta
    proposta: proposal.name,
    nome_proposta: proposal.name,
    orcamento: proposal.budget
      ? formatCurrency(Number(proposal.budget))
      : "R$ 0,00",
    valor_proposta: proposal.budget
      ? formatCurrency(Number(proposal.budget))
      : "R$ 0,00",
    valor_proposta_extenso: valorPropostaExtenso,
    valor_80_porcento: formatCurrency(valor80Porcento),
    valor_80_porcento_extenso: valor80PorcentoExtenso,

    // Informações de pagamento
    entrada: proposal.downPayment
      ? formatCurrency(Number(proposal.downPayment))
      : "R$ 0,00",
    valor_entrada: proposal.downPayment
      ? formatCurrency(Number(proposal.downPayment))
      : "R$ 0,00",
    valor_entrada_extenso: valorEntradaExtenso,
    num_parcelas: proposal.installmentNumber || "0",
    quantidade_parcelas: proposal.installmentNumber || "0",
    valor_parcela: proposal.installmentAmount
      ? formatCurrency(Number(proposal.installmentAmount))
      : "R$ 0,00",
    valor_parcela_extenso: valorParcelaExtenso,

    // Informações de prazo
    prazo_dias: diffDays.toString(),
    data_inicio: formatDate(proposal.startDate),
    data_fim: formatDate(proposal.endDate),

    // Informações do cliente
    nome_cliente: proposal.customer?.name || "",
    nome_cliente_maiusculo: (proposal.customer?.name || "").toUpperCase(),
    cpf_cnpj: proposal.customer?.document || "",
    endereco: enderecoCompleto,
    endereco_completo: enderecoCompleto,
    endereco_maiusculo: enderecoCompleto.toUpperCase(),

    // Outras informações
    data_hoje: formatDate(Date.now()),
    data_hoje_extenso: formatDate(Date.now()), // Formato por extenso: dia de mês de ano
  };
}
