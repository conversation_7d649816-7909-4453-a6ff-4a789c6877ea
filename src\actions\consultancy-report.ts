"use server";

import { prisma } from "@/src/lib/prisma";
import { formatDate, toKebabCase } from "@/src/lib/utils";
import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { MINIO_BUCKET_NAME, MINIO_ENDPOINT } from "@/src/lib/env/variables";

// Função para buscar dados de pluviosidade para uma inspeção
async function getPluviosityForInspection(city: string, inspectionDate: Date) {
  const dateKey = inspectionDate.toISOString().split("T")[0];
  const pluviosity = await prisma.pluviosity.findFirst({
    where: {
      dateKey,
      city: toKebabCase(city),
    },
    select: {
      value: true,
    },
  });

  return pluviosity?.value?.toString() ?? "0";
}

// Função para determinar a condição climática com base no valor de pluviosidade
function getWeatherCondition(pluviosityValue: string): string {
  const value = parseFloat(pluviosityValue);

  if (value === 0) return "Dia claro, sem precipitação";
  if (value <= 0.2) return "Garoa leve";
  if (value <= 2.5) return "Chuva fraca";
  if (value <= 7.6) return "Chuva moderada";
  if (value <= 50) return "Chuva forte";
  return "Chuva muito forte";
}

// Função para determinar se é praticável
function getWorkabilityCondition(pluviosityValue: string): string {
  const value = parseFloat(pluviosityValue);
  return value <= 2.5 ? "Praticável" : "Não Praticável";
}

export async function generateConsultancyReport(
  reportTemplateId: string,
  params: any
) {
  try {
    const { proposalId, inspectionParameterId } = params;

    if (!proposalId) {
      throw new Error("ProposalId é obrigatório");
    }

    const reportTemplate = await prisma.reportTemplate.findUnique({
      where: { id: reportTemplateId },
      include: { fileEditor: true },
    });

    const data = await generateReport(proposalId, inspectionParameterId);

    const fileName = `${data.dia}${data.mes}${data.ano}-${reportTemplate?.title}`;

    const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
      `${reportTemplate?.fileEditorId}`,
      data,
      fileName
    );

    if (!replacedFileEditor?.id) throw Error("Failed to save file editor");
    const fileEditorId = replacedFileEditor?.id;
    return {
      fileEditorId,
    };
  } catch (error) {
    console.error("Error generating consultancy report:", error);
    throw error;
  }
}

export async function generateReport(
  proposalId: string,
  inspectionParameterId?: string
) {
  try {
    // Buscar a proposta com o arquivo principal
    const data = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
      },
      include: {
        customer: true,
        file: true,
        // Buscar parâmetros de inspeção e fotos relacionadas
        inspectionParameters: {
          include: {
            photos: {
              include: {
                file: true,
              },
            },
          },
        },
      },
    });

    if (!data) {
      throw new Error("Proposal not found");
    }

    // Se um inspectionParameterId específico foi fornecido, filtrar apenas esse parâmetro
    if (inspectionParameterId) {
      data.inspectionParameters = data.inspectionParameters.filter(
        (param) => param.id === inspectionParameterId
      );
    }

    // Gerar texto do relatório com base nos dados da consultoria
    const reportText = await generateReportText(data);

    // Gerar recomendações com base nos dados da consultoria
    const recommendations = await generateRecommendations(data);

    // Log para depuração - verificar arquivos encontrados
    console.log("Dados do relatório de consultoria:", {
      mainFile: data.file ? data.file.path : "Nenhum arquivo principal",
      inspectionPhotos: data.inspectionParameters
        ? data.inspectionParameters.flatMap((param) =>
            param.photos.map((photo) => photo.file?.path || "Sem caminho")
          )
        : "Nenhuma foto de inspeção",
      inspectionParameterId: inspectionParameterId || "Todos os parâmetros",
      totalInspectionParameters: data.inspectionParameters?.length || 0,
      filteredInspectionIds:
        data.inspectionParameters?.map((param) => param.id) || [],
    });

    // Usar os parâmetros de inspeção já filtrados dos dados da proposta
    const inspectionParameters = data.inspectionParameters;

    // Buscar dados completos de mão de obra e equipamentos para os parâmetros filtrados
    const inspectionParametersWithLabor =
      await prisma.inspectionParameter.findMany({
        where: {
          id: {
            in: inspectionParameters.map((param) => param.id),
          },
        },
        include: {
          laborEquipament: {
            include: {
              labor: true,
            },
          },
        },
        orderBy: {
          inspectionDate: "asc",
        },
      });

    // Formatar dados técnicos das inspeções para o resumo de atividades de consultoria
    const resumoAtividades = inspectionParameters
      .map((item, index) => {
        const date = formatDate(item.inspectionDate, "DATE");
        return `${index + 1}. ${date} - ${item.technicalData}`;
      })
      .join("\n");

    // Log para confirmar que estamos usando apenas os dados filtrados
    console.log("Resumo de atividades de consultoria gerado:", {
      totalParametros: inspectionParameters.length,
      parametrosIds: inspectionParameters.map((p) => p.id),
      resumoAtividades: resumoAtividades || "Vazio",
      numeroRelatorio:
        inspectionParameters.length > 0
          ? inspectionParameters[0].numberInspection
          : "N/A",
    });

    // Consolidar todos os dados de mão de obra e equipamentos dos parâmetros filtrados
    const allLaborEquipment = inspectionParametersWithLabor.flatMap(
      (param) => param.laborEquipament || []
    );

    // Separar mão de obra e equipamentos
    const laborData = allLaborEquipment.filter(
      (item) => item.labor.type === "LABOR"
    );
    const equipmentData = allLaborEquipment.filter(
      (item) => item.labor.type === "EQUIPAMENT"
    );

    // Consolidar quantidades por tipo (somar quantidades de itens iguais)
    const consolidatedLabor = laborData.reduce((acc, item) => {
      const key = item.labor.name;
      if (acc[key]) {
        acc[key].amount += item.amount || 0;
      } else {
        acc[key] = {
          name: item.labor.name,
          description: item.labor.description,
          amount: item.amount || 0,
          type: "LABOR",
        };
      }
      return acc;
    }, {} as Record<string, any>);

    const consolidatedEquipment = equipmentData.reduce((acc, item) => {
      const key = item.labor.name;
      if (acc[key]) {
        acc[key].amount += item.amount || 0;
      } else {
        acc[key] = {
          name: item.labor.name,
          description: item.labor.description,
          amount: item.amount || 0,
          type: "EQUIPAMENT",
        };
      }
      return acc;
    }, {} as Record<string, any>);

    // Formatar listagem de mão de obra e equipamentos
    const laborList = Object.values(consolidatedLabor)
      .map(
        (item: any) =>
          `• ${item.name}: ${item.amount} ${
            item.description ? `(${item.description})` : ""
          }`
      )
      .join("\n");

    const equipmentList = Object.values(consolidatedEquipment)
      .map(
        (item: any) =>
          `• ${item.name}: ${item.amount} ${
            item.description ? `(${item.description})` : ""
          }`
      )
      .join("\n");

    const maoObraEquipamentos =
      [
        laborList ? `Mão de Obra:\n${laborList}` : "Sem mão de obra",
        equipmentList ? `Equipamentos:\n${equipmentList}` : "Sem equipamentos",
      ]
        .filter(Boolean)
        .join("\n\n") ||
      (inspectionParameterId
        ? "Nenhuma mão de obra ou equipamento registrado para o parâmetro de inspeção selecionado."
        : "Nenhuma mão de obra ou equipamento registrado para esta consultoria.");

    // Buscar dados climáticos para a data da inspeção e localização da proposta
    let dadosClimaticos = "Dados climáticos não disponíveis";
    let precipitacao = "0";
    let condicaoClimatica = "Não informado";
    let situacaoTrabalhabilidade = "Não informado";

    try {
      if (data.city && inspectionParameters.length > 0) {
        // Usar a data da primeira inspeção (que será a única se um parâmetro específico foi selecionado)
        const inspectionDate = new Date(inspectionParameters[0].inspectionDate);
        precipitacao = await getPluviosityForInspection(
          data.city,
          inspectionDate
        );
        condicaoClimatica = getWeatherCondition(precipitacao);
        situacaoTrabalhabilidade = getWorkabilityCondition(precipitacao);

        dadosClimaticos = `Precipitação: ${precipitacao}mm | Condição: ${condicaoClimatica} | Situação: ${situacaoTrabalhabilidade}`;

        console.log("Dados climáticos obtidos para consultoria:", {
          cidade: data.city,
          dataInspecao: formatDate(inspectionDate, "DATE"),
          precipitacao,
          condicaoClimatica,
          situacaoTrabalhabilidade,
        });
      }
    } catch (error) {
      console.error("Erro ao obter dados climáticos:", error);
    }

    const variables: any = {
      obra: data.name,
      nome: data.customer.name,
      cnpj: data.customer.document,
      cliente: data.customer.name,
      endereco: data.address ?? "",
      cidade: data.city ?? "",
      estado: data.state ?? "",
      cep: data.cep,
      relatorio: reportText,
      recomendacoes: recommendations,
      resumo_atividades:
        resumoAtividades ||
        (inspectionParameterId
          ? "Nenhuma atividade de consultoria encontrada para o parâmetro selecionado."
          : "Nenhuma atividade de consultoria registrada para este projeto."),
      mao_obra_equipamentos: maoObraEquipamentos,
      numero_relatorio:
        inspectionParameters.length > 0
          ? inspectionParameters[0].numberInspection || ""
          : "",
      dados_climaticos: dadosClimaticos,
      precipitacao: precipitacao,
      condicao_climatica: condicaoClimatica,
      situacao_trabalhabilidade: situacaoTrabalhabilidade,
      dataHoje: formatDate(new Date(), "DATE"),
      dia: new Date(data.startDate).getDate(),
      mes: new Date(data.startDate).toLocaleString("pt-BR", { month: "long" }),
      ano: new Date(data.startDate).getFullYear(),
      diaFinal: new Date(data.endDate).getDate(),
      mesFinal: new Date(data.endDate).toLocaleString("pt-BR", {
        month: "long",
      }),
      anoFinal: new Date(data.endDate).getFullYear(),
      prazoContratual: `${Math.ceil(
        (new Date(data.endDate).getTime() -
          new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoDecorrido: `${Math.ceil(
        (new Date().getTime() - new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoVencer: `${Math.ceil(
        (new Date(data.endDate).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      items: [
        // Incluir o arquivo principal do projeto, se existir
        ...(data.file
          ? [
              {
                image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${data.file.path}`,
                numero: 1,
                legenda: "Arquivo principal da consultoria",
              },
            ]
          : []),
        // Incluir todas as fotos dos parâmetros de inspeção
        ...data.inspectionParameters.flatMap((param, paramIndex) =>
          param.photos
            .filter((photo) => photo.file && photo.file.path) // Filtrar apenas fotos com arquivo válido
            .map((photo, photoIndex) => ({
              image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
              numero: data.file
                ? paramIndex + photoIndex + 2
                : paramIndex + photoIndex + 1,
              legenda:
                photo.description ||
                `Imagem ${paramIndex + photoIndex + 1} da consultoria`,
            }))
        ),
      ],
      valorTotal: formatCurrency(Number(data.workTotalCost || 0)),
      valorServico: formatCurrency(Number(data.budget || 0)),
      tipoServico: formatServiceType(data.serviceType || "CONSULTORIA"),
      descricaoServico:
        data.customService || "Serviços de consultoria em engenharia",
    };

    // Log para depuração - verificar as imagens que serão incluídas no relatório
    console.log("Items para o relatório de consultoria:", variables.items);

    return variables;
  } catch (err) {
    console.error(err);
    throw err;
  }
}

function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

function formatServiceType(type: string): string {
  const types: Record<string, string> = {
    PROJECT: "Projeto",
    INSPECAO: "Inspeção",
    CONSULTORIA: "Consultoria",
    FISCALIZACAO: "Fiscalização",
    GERENCIAMENTO: "Gerenciamento",
  };

  return types[type] || type;
}

async function generateReportText(proposal: any): Promise<string> {
  const prompt = `Você é um engenheiro civil especialista em consultoria técnica.
  Gere um texto detalhado para um relatório de consultoria com base nas seguintes informações:

  Nome do projeto: ${proposal.name}
  Cliente: ${proposal.customer.name}
  Endereço: ${proposal.address}, ${proposal.city}, ${proposal.state}
  Tipo de serviço: ${formatServiceType(proposal.serviceType)}
  Descrição do serviço: ${
    proposal.customService || "Serviços de consultoria em engenharia"
  }
  Valor do serviço: ${formatCurrency(proposal.serviceCost || 0)}
  Valor total da obra: ${formatCurrency(proposal.workTotalCost || 0)}
  Data de início: ${formatDate(proposal.startDate, "DATE")}
  Data de término prevista: ${formatDate(proposal.endDate, "DATE")}

  Gere um texto com vários parágrafos descrevendo os serviços de consultoria prestados, análises técnicas realizadas, metodologias aplicadas e resultados obtidos, utilizando termos técnicos da área de engenharia civil e consultoria.`;

  try {
    const response = await fetch("https://api.openai.com/v1/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo-instruct",
        prompt,
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].text.trim();
  } catch (error) {
    console.error("Error generating consultancy report text:", error);
    return "Não foi possível gerar o texto do relatório de consultoria. Por favor, tente novamente mais tarde.";
  }
}

async function generateRecommendations(proposal: any): Promise<string> {
  const prompt = `Você é um engenheiro civil especialista em consultoria técnica.
  Com base nas seguintes informações de um projeto de consultoria, sugira recomendações técnicas específicas e próximos passos:

  Nome do projeto: ${proposal.name}
  Cliente: ${proposal.customer.name}
  Endereço: ${proposal.address}, ${proposal.city}, ${proposal.state}
  Tipo de serviço: ${formatServiceType(proposal.serviceType)}
  Descrição do serviço: ${
    proposal.customService || "Serviços de consultoria em engenharia"
  }

  Faça um breve parágrafo resumindo as recomendações técnicas e, em seguida, crie uma sequência de bullets descrevendo cada recomendação detalhadamente, focando em aspectos de consultoria, análise técnica, normas aplicáveis e melhores práticas. Finalize com uma conclusão.`;

  try {
    const response = await fetch("https://api.openai.com/v1/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo-instruct",
        prompt,
        max_tokens: 600,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].text.trim();
  } catch (error) {
    console.error("Error generating consultancy recommendations:", error);
    return "Não foi possível gerar as recomendações de consultoria. Por favor, tente novamente mais tarde.";
  }
}
